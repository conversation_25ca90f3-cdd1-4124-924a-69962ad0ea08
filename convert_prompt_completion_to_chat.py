# convert_prompt_completion_to_chat.py
import json, sys, pathlib

src = pathlib.Path("skygeek.com_tools-equipment_100-back-countersink.jsonl")
dst = pathlib.Path("skygeek_chat_fixed.jsonl")

with src.open() as fin, dst.open("w") as fout:
    for ln, line in enumerate(fin, 1):
        try:
            obj = json.loads(line)
            prompt = obj["prompt"].strip()
            completion = obj["completion"].strip()
            new_obj = {
                "messages": [
                    {"role": "user", "content": prompt},
                    {"role": "assistant", "content": completion}
                ]
            }
            fout.write(json.dumps(new_obj, ensure_ascii=False) + "\n")
        except Exception as e:
            print(f"Line {ln} failed: {e}", file=sys.stderr)
            continue

print(f"Converted → {dst}")
